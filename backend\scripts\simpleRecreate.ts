/**
 * Script simplu pentru refacerea bazei de date cu CUID-uri standard
 * Folosește doar datele minime necesare pentru testare
 */

import { PrismaClient } from '@prisma/client';
import { createId } from '@paralleldrive/cuid2';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🚀 Refacerea simplă a bazei de date cu CUID-uri standard...\n');

  try {
    // Pasul 1: Șterge toate datele existente
    console.log('🗑️  Ștergerea datelor existente...');
    await prisma.expense.deleteMany();
    await prisma.category.deleteMany();
    await prisma.subscription.deleteMany();
    await prisma.usageLog.deleteMany();
    await prisma.webhookEvent.deleteMany();
    await prisma.user.deleteMany();
    await prisma.plan.deleteMany();
    console.log('✅ Toate datele au fost șterse cu succes');

    // Pasul 2: Creează un plan simplu
    console.log('📋 Crearea planului de test...');
    const plan = await prisma.plan.create({
      data: {
        id: createId(),
        stripeId: 'price_test_free',
        name: 'Free Test',
        description: 'Plan de test gratuit',
        price: 0,
        currency: 'USD',
        interval: 'month',
        features: ['Test features'],
        limits: { monthlyExpenses: 50 },
        isActive: true,
        sortOrder: 1,
      },
    });
    console.log(`✅ Plan creat: ${plan.name} (${plan.id})`);

    // Pasul 3: Creează un utilizator de test
    console.log('👤 Crearea utilizatorului de test...');
    const hashedPassword = await bcrypt.hash('password123', 10);
    const user = await prisma.user.create({
      data: {
        id: createId(),
        email: '<EMAIL>',
        password: hashedPassword,
        firstName: 'Test',
        lastName: 'User',
        role: 'user',
        currency: 'USD',
        timezone: 'UTC',
        isActive: true,
        emailVerified: true,
        planType: 'free',
      },
    });
    console.log(`✅ Utilizator creat: ${user.firstName} ${user.lastName} (${user.id})`);

    // Pasul 4: Creează o categorie de test
    console.log('📂 Crearea categoriei de test...');
    const category = await prisma.category.create({
      data: {
        id: createId(),
        name: 'Test Category',
        description: 'Categorie de test',
        color: '#FF6B6B',
        icon: 'test',
        userId: user.id,
        sortOrder: 1,
        isActive: true,
        isDefault: true,
      },
    });
    console.log(`✅ Categorie creată: ${category.name} (${category.id})`);

    // Pasul 5: Creează o cheltuială de test
    console.log('💰 Crearea cheltuielii de test...');
    const expense = await prisma.expense.create({
      data: {
        id: createId(),
        description: 'Test Expense',
        amount: 25.50,
        date: new Date(),
        paymentMethod: 'card',
        userId: user.id,
        categoryId: category.id,
        tags: [],
        isRecurring: false,
      },
    });
    console.log(`✅ Cheltuială creată: ${expense.description} (${expense.id})`);

    // Pasul 6: Verifică formatul CUID2
    console.log('🔍 Verificarea formatului CUID2...');
    const cuidRegex = /^c[a-z0-9]{24}$/;
    
    const allValid = [user.id, category.id, expense.id, plan.id].every(id => cuidRegex.test(id));
    
    if (allValid) {
      console.log('✅ Toate ID-urile sunt în format CUID2 standard!');
      console.log(`📊 Statistici finale:`);
      console.log(`   - Utilizatori creați: 1`);
      console.log(`   - Categorii create: 1`);
      console.log(`   - Cheltuieli create: 1`);
      console.log(`   - Planuri create: 1`);
      console.log(`   - Toate ID-urile sunt în format CUID2 standard ✅`);
      
      console.log('\n🎉 Refacerea bazei de date s-a finalizat cu succes!');
      console.log('\n📝 Date de test create:');
      console.log(`   - Email: <EMAIL>`);
      console.log(`   - Parolă: password123`);
      console.log(`   - Utilizator: ${user.firstName} ${user.lastName}`);
      console.log(`   - Categorie: ${category.name}`);
      console.log(`   - Cheltuială: ${expense.description} - $${expense.amount}`);
    } else {
      throw new Error('ID-urile generate nu sunt în format CUID2 standard');
    }

  } catch (error) {
    console.error('\n❌ Eroare critică în timpul refacerii:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Rulează scriptul dacă este apelat direct
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ Eroare neașteptată:', error);
    process.exit(1);
  });
}

export { main as simpleRecreate };
