/**
 * Middleware pentru validări duale (snake_case și camelCase)
 * Permite acceptarea ambelor formate în timpul tranziției
 */

import { Request, Response, NextFunction } from 'express';
import <PERSON><PERSON> from 'joi';
import { CaseConverter } from '../utils/caseConverter';

// Tipuri pentru validarea duală
interface DualValidationOptions {
  preferredFormat?: 'snake' | 'camel';
  allowBothFormats?: boolean;
  transformToPreferred?: boolean;
  logValidations?: boolean;
}

// Configurația implicită
const DEFAULT_VALIDATION_OPTIONS: DualValidationOptions = {
  preferredFormat: 'snake',
  allowBothFormats: true,
  transformToPreferred: true,
  logValidations: process.env.NODE_ENV === 'development'
};

/**
 * Creează o schemă Joi duală care acceptă atât snake_case cât și camelCase
 */
export function createDualSchema(snakeSchema: Joi.ObjectSchema): Joi.AlternativesSchema {
  // Extrage descrierea schemei snake_case
  const snakeDescription = snakeSchema.describe() as any;

  // Creează schema camelCase echivalentă
  const camelSchema = convertSchemaKeys(snakeDescription, 'snake_to_camel');

  // Returnează o schemă care acceptă ambele formate
  return Joi.alternatives().try(
    snakeSchema,
    Joi.object(camelSchema)
  );
}

/**
 * Convertește cheile unei scheme Joi
 */
function convertSchemaKeys(schemaDescription: any, direction: 'snake_to_camel' | 'camel_to_snake'): any {
  if (!schemaDescription || typeof schemaDescription !== 'object') {
    return schemaDescription;
  }
  
  if (schemaDescription.type === 'object' && schemaDescription.keys) {
    const convertedKeys: any = {};
    
    for (const [key, value] of Object.entries(schemaDescription.keys)) {
      const convertedKey = direction === 'snake_to_camel' 
        ? CaseConverter.stringToCamel(key)
        : CaseConverter.stringToSnake(key);
      
      convertedKeys[convertedKey] = convertSchemaKeys(value, direction);
    }
    
    return convertedKeys;
  }
  
  if (schemaDescription.type === 'array' && schemaDescription.items) {
    return {
      ...schemaDescription,
      items: schemaDescription.items.map((item: any) => convertSchemaKeys(item, direction))
    };
  }
  
  return schemaDescription;
}

/**
 * Middleware pentru validare duală cu transformare automată
 */
export function validateDual(
  snakeSchema: Joi.ObjectSchema, 
  options: DualValidationOptions = {}
) {
  const config = { ...DEFAULT_VALIDATION_OPTIONS, ...options };
  
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      // Încearcă validarea cu schema snake_case
      const snakeValidation = snakeSchema.validate(req.body, { abortEarly: false });
      
      // Încearcă validarea cu schema camelCase
      const camelBody = CaseConverter.toCamel(req.body);
      const camelSchema = createCamelCaseSchema(snakeSchema);
      const camelValidation = camelSchema.validate(camelBody, { abortEarly: false });
      
      // Determină care validare a reușit
      let validationResult;
      let detectedFormat: 'snake' | 'camel' | 'mixed';
      
      if (!snakeValidation.error) {
        validationResult = snakeValidation;
        detectedFormat = 'snake';
      } else if (!camelValidation.error) {
        validationResult = camelValidation;
        detectedFormat = 'camel';
        
        // Transformă body-ul la formatul preferat dacă este necesar
        if (config.transformToPreferred && config.preferredFormat === 'snake') {
          req.body = CaseConverter.toSnake(camelBody);
        }
      } else {
        // Ambele validări au eșuat - returnează eroarea mai detaliată
        validationResult = snakeValidation.error.details.length <= camelValidation.error.details.length 
          ? snakeValidation 
          : camelValidation;
        detectedFormat = 'mixed';
      }
      
      if (config.logValidations) {
        console.log('🔍 Dual validation result:', {
          path: req.path,
          method: req.method,
          detectedFormat,
          snakeErrors: snakeValidation.error?.details.length || 0,
          camelErrors: camelValidation.error?.details.length || 0,
          success: !validationResult.error
        });
      }
      
      // Dacă validarea a eșuat, returnează eroarea
      if (validationResult.error) {
        const errors = validationResult.error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message,
          value: detail.context?.value
        }));

        res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors,
          hint: detectedFormat === 'mixed'
            ? 'Please use consistent naming convention (either snake_case or camelCase)'
            : undefined
        });
        return;
      }
      
      // Adaugă informații despre formatul detectat la request
      (req as any).detectedFormat = detectedFormat;
      
      next();
    } catch (error) {
      console.error('❌ Error in dual validation middleware:', error);
      next(error);
    }
  };
}

/**
 * Creează o schemă camelCase din una snake_case
 */
function createCamelCaseSchema(snakeSchema: Joi.ObjectSchema): Joi.ObjectSchema {
  const description = snakeSchema.describe() as any;
  
  if (description.type !== 'object' || !description.keys) {
    return snakeSchema;
  }
  
  const camelKeys: any = {};
  
  for (const [snakeKey, schemaValue] of Object.entries(description.keys)) {
    const camelKey = CaseConverter.stringToCamel(snakeKey);
    camelKeys[camelKey] = recreateJoiSchema(schemaValue);
  }
  
  return Joi.object(camelKeys);
}

/**
 * Recreează o schemă Joi din descrierea sa
 */
function recreateJoiSchema(description: any): Joi.Schema {
  if (!description || typeof description !== 'object') {
    return Joi.any();
  }
  
  let schema: Joi.Schema;
  
  switch (description.type) {
    case 'string':
      schema = Joi.string();
      if (description.rules) {
        description.rules.forEach((rule: any) => {
          switch (rule.name) {
            case 'min':
              schema = (schema as Joi.StringSchema).min(rule.args.limit);
              break;
            case 'max':
              schema = (schema as Joi.StringSchema).max(rule.args.limit);
              break;
            case 'email':
              schema = (schema as Joi.StringSchema).email();
              break;
            case 'pattern':
              schema = (schema as Joi.StringSchema).pattern(rule.args.regex);
              break;
          }
        });
      }
      break;
      
    case 'number':
      schema = Joi.number();
      if (description.rules) {
        description.rules.forEach((rule: any) => {
          switch (rule.name) {
            case 'min':
              schema = (schema as Joi.NumberSchema).min(rule.args.limit);
              break;
            case 'max':
              schema = (schema as Joi.NumberSchema).max(rule.args.limit);
              break;
            case 'positive':
              schema = (schema as Joi.NumberSchema).positive();
              break;
            case 'integer':
              schema = (schema as Joi.NumberSchema).integer();
              break;
          }
        });
      }
      break;
      
    case 'boolean':
      schema = Joi.boolean();
      break;
      
    case 'date':
      schema = Joi.date();
      break;
      
    case 'array':
      schema = Joi.array();
      if (description.items && description.items.length > 0) {
        const itemSchema = recreateJoiSchema(description.items[0]);
        schema = (schema as Joi.ArraySchema).items(itemSchema);
      }
      break;
      
    case 'object':
      if (description.keys) {
        const objectKeys: any = {};
        for (const [key, value] of Object.entries(description.keys)) {
          objectKeys[key] = recreateJoiSchema(value);
        }
        schema = Joi.object(objectKeys);
      } else {
        schema = Joi.object();
      }
      break;
      
    default:
      schema = Joi.any();
  }
  
  // Aplică flags comune
  if (description.flags) {
    if (description.flags.presence === 'required') {
      schema = schema.required();
    } else if (description.flags.presence === 'optional') {
      schema = schema.optional();
    }
    
    if (description.flags.default !== undefined) {
      schema = schema.default(description.flags.default);
    }
  }
  
  return schema;
}

/**
 * Utilitare pentru crearea rapidă de scheme duale
 */
export const DualSchemaBuilder = {
  /**
   * Creează o schemă care acceptă atât snake_case cât și camelCase
   */
  create: (snakeSchema: Joi.ObjectSchema) => createDualSchema(snakeSchema),
  
  /**
   * Convertește o schemă existentă la camelCase
   */
  toCamelCase: (snakeSchema: Joi.ObjectSchema) => createCamelCaseSchema(snakeSchema),
  
  /**
   * Validează un obiect cu ambele formate și returnează rezultatul
   */
  validate: (
    data: any, 
    snakeSchema: Joi.ObjectSchema, 
    options: DualValidationOptions = {}
  ) => {
    const config = { ...DEFAULT_VALIDATION_OPTIONS, ...options };
    
    const snakeResult = snakeSchema.validate(data, { abortEarly: false });
    
    if (!snakeResult.error) {
      return { 
        success: true, 
        data: snakeResult.value, 
        format: 'snake' as const,
        errors: null 
      };
    }
    
    const camelData = CaseConverter.toCamel(data);
    const camelSchema = createCamelCaseSchema(snakeSchema);
    const camelResult = camelSchema.validate(camelData, { abortEarly: false });
    
    if (!camelResult.error) {
      const finalData = config.transformToPreferred && config.preferredFormat === 'snake'
        ? CaseConverter.toSnake(camelResult.value)
        : camelResult.value;
        
      return { 
        success: true, 
        data: finalData, 
        format: 'camel' as const,
        errors: null 
      };
    }
    
    return {
      success: false,
      data: null,
      format: 'invalid' as const,
      errors: snakeResult.error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }))
    };
  }
};

export default {
  validateDual,
  createDualSchema,
  DualSchemaBuilder
};
