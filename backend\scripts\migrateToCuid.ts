/**
 * Script pentru migrarea datelor existente de la ID-uri numerice la CUID
 * și de la name la firstName/lastName
 */

import { PrismaClient } from '@prisma/client';
import { createId } from '@paralleldrive/cuid2';

const prisma = new PrismaClient();

interface MigrationStats {
  usersProcessed: number;
  categoriesProcessed: number;
  expensesProcessed: number;
  subscriptionsProcessed: number;
  usageLogsProcessed: number;
  webhookEventsProcessed: number;
  errors: string[];
}

/**
 * Verifică dacă datele sunt deja în format CUID
 */
async function checkCurrentDataFormat(): Promise<{
  needsMigration: boolean;
  hasNumericIds: boolean;
  hasNameField: boolean;
}> {
  try {
    // Verifică dacă există utilizatori cu ID-uri numerice
    const numericUsers = await prisma.$queryRaw<Array<{ id: string }>>`
      SELECT id FROM users WHERE id ~ '^[0-9]+$' LIMIT 1
    `;

    // Verifică dacă există câmpul name în loc de firstName/lastName
    const usersWithName = await prisma.$queryRaw<Array<{ name: string }>>`
      SELECT name FROM users WHERE name IS NOT NULL LIMIT 1
    `;

    const hasNumericIds = numericUsers.length > 0;
    const hasNameField = usersWithName.length > 0;
    const needsMigration = hasNumericIds || hasNameField;

    console.log('📊 Starea actuală a datelor:');
    console.log(`   - ID-uri numerice: ${hasNumericIds ? '✅ Da' : '❌ Nu'}`);
    console.log(`   - Câmp name: ${hasNameField ? '✅ Da' : '❌ Nu'}`);
    console.log(`   - Necesită migrare: ${needsMigration ? '✅ Da' : '❌ Nu'}`);

    return { needsMigration, hasNumericIds, hasNameField };
  } catch (error) {
    console.error('❌ Eroare la verificarea formatului datelor:', error);
    return { needsMigration: false, hasNumericIds: false, hasNameField: false };
  }
}

/**
 * Migrează utilizatorii de la name la firstName/lastName
 */
async function migrateUserNames(): Promise<number> {
  console.log('👤 Migrarea numelor utilizatorilor...');
  
  try {
    // Găsește utilizatorii care au câmpul name
    const usersWithName = await prisma.$queryRaw<Array<{
      id: string;
      name: string;
      firstName?: string;
      lastName?: string;
    }>>`
      SELECT id, name, firstName, lastName 
      FROM users 
      WHERE name IS NOT NULL AND (firstName IS NULL OR lastName IS NULL)
    `;

    let processed = 0;

    for (const user of usersWithName) {
      const nameParts = user.name.trim().split(' ');
      const firstName = nameParts[0] || user.name;
      const lastName = nameParts.slice(1).join(' ') || '';

      await prisma.user.update({
        where: { id: user.id },
        data: {
          firstName,
          lastName,
        },
      });

      processed++;
      console.log(`   ✅ Utilizator ${user.id}: "${user.name}" → "${firstName}" + "${lastName}"`);
    }

    console.log(`✅ ${processed} utilizatori procesați pentru migrarea numelor`);
    return processed;
  } catch (error) {
    console.error('❌ Eroare la migrarea numelor:', error);
    throw error;
  }
}

/**
 * Migrează ID-urile numerice la CUID
 */
async function migrateNumericIds(): Promise<MigrationStats> {
  console.log('🔄 Migrarea ID-urilor numerice la CUID...');
  
  const stats: MigrationStats = {
    usersProcessed: 0,
    categoriesProcessed: 0,
    expensesProcessed: 0,
    subscriptionsProcessed: 0,
    usageLogsProcessed: 0,
    webhookEventsProcessed: 0,
    errors: [],
  };

  try {
    // Această migrare este complexă și ar trebui făcută cu atenție
    // Pentru moment, doar verificăm și raportăm
    
    const numericUsers = await prisma.$queryRaw<Array<{ id: string }>>`
      SELECT id FROM users WHERE id ~ '^[0-9]+$'
    `;

    const numericCategories = await prisma.$queryRaw<Array<{ id: string }>>`
      SELECT id FROM categories WHERE id ~ '^[0-9]+$'
    `;

    const numericExpenses = await prisma.$queryRaw<Array<{ id: string }>>`
      SELECT id FROM expenses WHERE id ~ '^[0-9]+$'
    `;

    console.log(`📊 ID-uri numerice găsite:`);
    console.log(`   - Utilizatori: ${numericUsers.length}`);
    console.log(`   - Categorii: ${numericCategories.length}`);
    console.log(`   - Cheltuieli: ${numericExpenses.length}`);

    if (numericUsers.length > 0 || numericCategories.length > 0 || numericExpenses.length > 0) {
      console.log('⚠️  ATENȚIE: Migrarea ID-urilor numerice la CUID este complexă!');
      console.log('   Această operație ar trebui făcută cu un script dedicat și backup complet.');
      console.log('   Pentru moment, schema Prisma este configurată pentru CUID-uri noi.');
    }

    return stats;
  } catch (error) {
    console.error('❌ Eroare la migrarea ID-urilor:', error);
    stats.errors.push(`Eroare la migrarea ID-urilor: ${error}`);
    return stats;
  }
}

/**
 * Verifică consistența datelor după migrare
 */
async function verifyDataConsistency(): Promise<boolean> {
  console.log('🔍 Verificarea consistenței datelor...');
  
  try {
    // Verifică că toți utilizatorii au firstName și lastName
    const usersWithoutNames = await prisma.user.count({
      where: {
        OR: [
          { firstName: { equals: null } },
          { firstName: { equals: '' } },
          { lastName: { equals: null } },
        ],
      },
    });

    // Verifică că toate relațiile sunt valide
    const orphanedCategories = await prisma.category.count({
      where: {
        user: null,
      },
    });

    const orphanedExpenses = await prisma.expense.count({
      where: {
        OR: [
          { user: null },
          { category: null },
        ],
      },
    });

    console.log('📊 Rezultatele verificării:');
    console.log(`   - Utilizatori fără nume: ${usersWithoutNames}`);
    console.log(`   - Categorii orfane: ${orphanedCategories}`);
    console.log(`   - Cheltuieli orfane: ${orphanedExpenses}`);

    const isConsistent = usersWithoutNames === 0 && orphanedCategories === 0 && orphanedExpenses === 0;
    
    if (isConsistent) {
      console.log('✅ Datele sunt consistente!');
    } else {
      console.log('⚠️  Datele au probleme de consistență!');
    }

    return isConsistent;
  } catch (error) {
    console.error('❌ Eroare la verificarea consistenței:', error);
    return false;
  }
}

/**
 * Funcția principală de migrare
 */
async function main() {
  console.log('🚀 Începerea migrării la CUID și firstName/lastName...\n');

  try {
    // Verifică starea actuală
    const { needsMigration, hasNumericIds, hasNameField } = await checkCurrentDataFormat();

    if (!needsMigration) {
      console.log('✅ Datele sunt deja în formatul corect! Nu este necesară migrarea.');
      return;
    }

    // Migrează numele dacă este necesar
    if (hasNameField) {
      await migrateUserNames();
    }

    // Migrează ID-urile dacă este necesar
    if (hasNumericIds) {
      const stats = await migrateNumericIds();
      
      if (stats.errors.length > 0) {
        console.log('\n❌ Erori în timpul migrării:');
        stats.errors.forEach(error => console.log(`   - ${error}`));
      }
    }

    // Verifică consistența
    const isConsistent = await verifyDataConsistency();

    if (isConsistent) {
      console.log('\n🎉 Migrarea s-a finalizat cu succes!');
    } else {
      console.log('\n⚠️  Migrarea s-a finalizat cu probleme. Verifică manual datele.');
    }

  } catch (error) {
    console.error('\n❌ Eroare critică în timpul migrării:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Rulează scriptul dacă este apelat direct
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ Eroare neașteptată:', error);
    process.exit(1);
  });
}

export { main as migrateToCuid, checkCurrentDataFormat, verifyDataConsistency };
